Stack trace:
Frame         Function      Args
0007FFFFAC00  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9B00) msys-2.0.dll+0x1FE8E
0007FFFFAC00  0002100467F9 (000000000000, 000000000000, 000210059F6C, 0007FFFFAED8) msys-2.0.dll+0x67F9
0007FFFFAC00  000210046832 (0002102860C8, 0007FFFFAAB8, 000000000000, 0002102684E0) msys-2.0.dll+0x6832
0007FFFFAC00  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFAC00  000210068EFF (0007FFFFAC10, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28EFF
0007FFFFAEE0  00021006A225 (000000004000, 000000000000, 000000000008, 000000000008) msys-2.0.dll+0x2A225
0007FFFFAEE0  00021006A4A9 (000000000008, 0007FFFFAF88, 0007FFFFB164, 0000FFFFFFFF) msys-2.0.dll+0x2A4A9
0007FFFFAEE0  000210193F2B (000000000008, 0007FFFFAF88, 0007FFFFB164, 0000FFFFFFFF) msys-2.0.dll+0x153F2B
0007FFFFAEE0  00010042DB65 (000A00000004, 0007FFFFB174, 00010040DD62, 000A0005D340) bash.exe+0x2DB65
0000FFFFFFFF  00010043C4F8 (0000000000C2, 000000000000, 000A00097E40, 000A00087430) bash.exe+0x3C4F8
000000000070  00010043E6BE (000000000000, 000A00000001, 000210178DD2, 0007FFFFB170) bash.exe+0x3E6BE
000000000070  000100441B06 (000700000001, 000200000000, 0007FFFFB260, 000000000000) bash.exe+0x41B06
000000000070  000100441D36 (000A00000000, 000000000000, 000000000000, 000000000000) bash.exe+0x41D36
000000000051  0001004449D8 (000000000000, 000000000010, 000210268720, 000A00049EE0) bash.exe+0x449D8
000000000051  00010043D697 (000000000010, 000000000000, 000210193F2B, 000A00096480) bash.exe+0x3D697
000000000010  00010043DC63 (000000000001, 000000000001, 000A001BE310, 000A001BE280) bash.exe+0x3DC63
000000000000  000100433786 (000000000001, 000000000000, 00010061F274, 000A0004A8E0) bash.exe+0x33786
000000000001  0001004440DE (000200000000, 000100000000, 000200000352, 000000000000) bash.exe+0x440DE
0000FFFFFFFF  000100419C92 (0001004358D4, 000000000000, 000000000010, 000A00097910) bash.exe+0x19C92
000A00097910  00010041C54A (00010048F6F5, 000210268720, 000100620700, 000A00097910) bash.exe+0x1C54A
0000FFFFFFFF  0001004179C7 (000210193F2B, 0000FFFFFFFF, 0001004F70ED, 000A00097910) bash.exe+0x179C7
000A00093DB0  00010041AC6A (000210178E7F, 000000000000, 0001004EB4A0, 000100000000) bash.exe+0x1AC6A
000A00093DB0  00010041C50F (000200000000, 000000000000, 000210178D00, 000A00093DB0) bash.exe+0x1C50F
0000FFFFFFFF  0001004179C7 (0001004F70ED, 000000000000, 00010044D0CA, 000A00093DB0) bash.exe+0x179C7
000000000160  00010041AC6A (000200000000, 000100000000, 000210178D00, 000000000000) bash.exe+0x1AC6A
000000000160  00010041813E (000210178DD2, 000000000000, 000210268720, 000A00096310) bash.exe+0x1813E
000A00096310  00010041C54A (000A00097AA0, 000210268720, 000100620700, 000A00096310) bash.exe+0x1C54A
0000FFFFFFFF  0001004179C7 (000A00022E50, 000A00097E40, 0001004F70ED, 000A00096310) bash.exe+0x179C7
000A000961D0  00010041AC6A (000A00097DE0, 000210268720, DFDFDFDFDFDFDFDF, 0001004F710E) bash.exe+0x1AC6A
000A000961D0  00010041C50F (00000000001F, 0007FFFFC160, 000000000004, 000A000961D0) bash.exe+0x1C50F
0000FFFFFFFF  0001004179C7 (0001004F70ED, 000000000000, 00010044D0CA, 000A000961D0) bash.exe+0x179C7
000000000160  00010041AC6A (000210178DD2, 000A00000000, 0001004261D4, 000A000941C0) bash.exe+0x1AC6A
End of stack trace (more stack frames may be present)
Loaded modules:
000100400000 bash.exe
7FFECAA90000 ntdll.dll
7FFECA070000 KERNEL32.DLL
7FFEC7FC0000 KERNELBASE.dll
7FFEC8A30000 USER32.dll
7FFEC86C0000 win32u.dll
7FFEC93F0000 GDI32.dll
7FFEC8420000 gdi32full.dll
7FFEC7C00000 msvcp_win.dll
7FFEC7EA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFECA1C0000 advapi32.dll
7FFEC8BF0000 msvcrt.dll
7FFECA900000 sechost.dll
7FFEC86F0000 bcrypt.dll
7FFEC9F50000 RPCRT4.dll
7FFEC7390000 CRYPTBASE.DLL
7FFEC83A0000 bcryptPrimitives.dll
7FFECA9C0000 IMM32.DLL
