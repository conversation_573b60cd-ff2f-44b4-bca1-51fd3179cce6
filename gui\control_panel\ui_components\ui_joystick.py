import tkinter as tk
from tkinter import ttk, messagebox
import threading

def setup_joystick_ui(control_panel):
    """
    Configura la interfaz de usuario para el control del joystick.
    
    Args:
        control_panel: Instancia de Gui_control_panel
    """
    # Crear frame para joystick en el notebook
    control_panel.frames.joystick = ttk.Frame(control_panel.notebook)
    control_panel.notebook.add(control_panel.frames.joystick, text="🎮 Joystick")
    
    # Configurar grid
    control_panel.frames.joystick.grid_rowconfigure(0, weight=0)  # Info
    control_panel.frames.joystick.grid_rowconfigure(1, weight=0)  # Controles
    control_panel.frames.joystick.grid_rowconfigure(2, weight=1)  # Log
    control_panel.frames.joystick.grid_columnconfigure(0, weight=1)
    
    # Crear secciones
    create_joystick_info_section(control_panel)
    create_joystick_controls_section(control_panel)
    create_joystick_log_section(control_panel)
    
    # Actualizar información inicial
    update_joystick_info(control_panel)

def create_joystick_info_section(control_panel):
    """Crea la sección de información del joystick"""
    # Frame principal para información
    info_frame = ttk.LabelFrame(control_panel.frames.joystick, text="📋 Información del Joystick", padding=10)
    info_frame.grid(row=0, column=0, sticky="ew", padx=10, pady=5)
    
    # Labels para mostrar información
    control_panel.joystick_info_labels = {}
    
    # Estado de conexión
    ttk.Label(info_frame, text="Estado:").grid(row=0, column=0, sticky="w", padx=(0, 10))
    control_panel.joystick_info_labels['status'] = ttk.Label(info_frame, text="❌ Desconectado", foreground="red")
    control_panel.joystick_info_labels['status'].grid(row=0, column=1, sticky="w")
    
    # Nombre del joystick
    ttk.Label(info_frame, text="Nombre:").grid(row=1, column=0, sticky="w", padx=(0, 10))
    control_panel.joystick_info_labels['name'] = ttk.Label(info_frame, text="N/A")
    control_panel.joystick_info_labels['name'].grid(row=1, column=1, sticky="w")
    
    # Número de botones
    ttk.Label(info_frame, text="Botones:").grid(row=2, column=0, sticky="w", padx=(0, 10))
    control_panel.joystick_info_labels['buttons'] = ttk.Label(info_frame, text="N/A")
    control_panel.joystick_info_labels['buttons'].grid(row=2, column=1, sticky="w")
    
    # Número de ejes
    ttk.Label(info_frame, text="Ejes:").grid(row=3, column=0, sticky="w", padx=(0, 10))
    control_panel.joystick_info_labels['axes'] = ttk.Label(info_frame, text="N/A")
    control_panel.joystick_info_labels['axes'].grid(row=3, column=1, sticky="w")

def create_joystick_controls_section(control_panel):
    """Crea la sección de controles del joystick"""
    # Frame principal para controles
    controls_frame = ttk.LabelFrame(control_panel.frames.joystick, text="🎛️ Controles", padding=10)
    controls_frame.grid(row=1, column=0, sticky="ew", padx=10, pady=5)
    
    # Configurar grid del frame de controles
    for i in range(4):
        controls_frame.grid_columnconfigure(i, weight=1)
    
    # Botón para detectar joysticks
    btn_detect = ttk.Button(controls_frame, text="🔍 Detectar Joysticks", 
                           command=lambda: detect_joysticks_action(control_panel))
    btn_detect.grid(row=0, column=0, padx=5, pady=5, sticky="ew")
    
    # Botón para conectar joystick
    control_panel.btn_connect = ttk.Button(controls_frame, text="🔌 Conectar", 
                                          command=lambda: connect_joystick_action(control_panel))
    control_panel.btn_connect.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
    
    # Botón para iniciar/parar escucha
    control_panel.btn_listen = ttk.Button(controls_frame, text="🎧 Iniciar Escucha", 
                                         command=lambda: toggle_listening_action(control_panel))
    control_panel.btn_listen.grid(row=0, column=2, padx=5, pady=5, sticky="ew")
    
    # Botón para probar botones
    control_panel.btn_test = ttk.Button(controls_frame, text="🧪 Probar Botones", 
                                       command=lambda: test_buttons_action(control_panel))
    control_panel.btn_test.grid(row=0, column=3, padx=5, pady=5, sticky="ew")

def create_joystick_log_section(control_panel):
    """Crea la sección de log del joystick"""
    # Frame principal para log
    log_frame = ttk.LabelFrame(control_panel.frames.joystick, text="📝 Log de Actividad", padding=10)
    log_frame.grid(row=2, column=0, sticky="nsew", padx=10, pady=5)
    
    # Configurar grid
    log_frame.grid_rowconfigure(0, weight=1)
    log_frame.grid_columnconfigure(0, weight=1)
    
    # Text widget para el log con scrollbar
    log_text_frame = ttk.Frame(log_frame)
    log_text_frame.grid(row=0, column=0, sticky="nsew")
    log_text_frame.grid_rowconfigure(0, weight=1)
    log_text_frame.grid_columnconfigure(0, weight=1)
    
    control_panel.joystick_log = tk.Text(log_text_frame, height=10, wrap=tk.WORD, state=tk.DISABLED)
    control_panel.joystick_log.grid(row=0, column=0, sticky="nsew")
    
    # Scrollbar para el log
    scrollbar = ttk.Scrollbar(log_text_frame, orient="vertical", command=control_panel.joystick_log.yview)
    scrollbar.grid(row=0, column=1, sticky="ns")
    control_panel.joystick_log.configure(yscrollcommand=scrollbar.set)
    
    # Botón para limpiar log
    btn_clear_log = ttk.Button(log_frame, text="🗑️ Limpiar Log", 
                              command=lambda: clear_joystick_log(control_panel))
    btn_clear_log.grid(row=1, column=0, pady=(10, 0))

def log_joystick_message(control_panel, message):
    """
    Agrega un mensaje al log del joystick.
    
    Args:
        control_panel: Instancia de Gui_control_panel
        message (str): Mensaje a agregar
    """
    if hasattr(control_panel, 'joystick_log'):
        control_panel.joystick_log.config(state=tk.NORMAL)
        control_panel.joystick_log.insert(tk.END, f"{message}\n")
        control_panel.joystick_log.see(tk.END)
        control_panel.joystick_log.config(state=tk.DISABLED)

def clear_joystick_log(control_panel):
    """Limpia el log del joystick"""
    control_panel.joystick_log.config(state=tk.NORMAL)
    control_panel.joystick_log.delete(1.0, tk.END)
    control_panel.joystick_log.config(state=tk.DISABLED)

def update_joystick_info(control_panel):
    """Actualiza la información mostrada del joystick"""
    if control_panel.joystick_controller.is_connected():
        info = control_panel.joystick_controller.get_joystick_info()
        control_panel.joystick_info_labels['status'].config(text="✅ Conectado", foreground="green")
        control_panel.joystick_info_labels['name'].config(text=info['name'])
        control_panel.joystick_info_labels['buttons'].config(text=str(info['num_buttons']))
        control_panel.joystick_info_labels['axes'].config(text=str(info['num_axes']))
        
        # Actualizar botones
        control_panel.btn_connect.config(text="🔌 Desconectar")
        control_panel.btn_test.config(state="normal")
        control_panel.btn_listen.config(state="normal")
    else:
        control_panel.joystick_info_labels['status'].config(text="❌ Desconectado", foreground="red")
        control_panel.joystick_info_labels['name'].config(text="N/A")
        control_panel.joystick_info_labels['buttons'].config(text="N/A")
        control_panel.joystick_info_labels['axes'].config(text="N/A")
        
        # Actualizar botones
        control_panel.btn_connect.config(text="🔌 Conectar")
        control_panel.btn_test.config(state="disabled")
        control_panel.btn_listen.config(state="disabled", text="🎧 Iniciar Escucha")

# Funciones de acción para los botones

def detect_joysticks_action(control_panel):
    """Acción para detectar joysticks"""
    joysticks = control_panel.joystick_controller.detect_joysticks()
    
    if joysticks:
        message = f"🔍 Encontrados {len(joysticks)} joystick(s):\n"
        for joy in joysticks:
            message += f"  • {joy['name']} (ID: {joy['id']})\n"
    else:
        message = "❌ No se encontraron joysticks conectados"
    
    log_joystick_message(control_panel, message)
    messagebox.showinfo("Detección de Joysticks", message)

def connect_joystick_action(control_panel):
    """Acción para conectar/desconectar joystick"""
    if control_panel.joystick_controller.is_connected():
        # Desconectar
        control_panel.joystick_controller.stop_listening()
        control_panel.joystick_controller.disconnect_joystick()
        log_joystick_message(control_panel, "🔌 Joystick desconectado")
    else:
        # Conectar
        if control_panel.joystick_controller.connect_joystick(0):
            log_joystick_message(control_panel, "✅ Joystick conectado exitosamente")
        else:
            log_joystick_message(control_panel, "❌ Error al conectar joystick")
    
    update_joystick_info(control_panel)

def toggle_listening_action(control_panel):
    """Acción para iniciar/parar la escucha del joystick"""
    if control_panel.joystick_controller.is_running:
        # Parar escucha
        control_panel.joystick_controller.stop_listening()
        control_panel.btn_listen.config(text="🎧 Iniciar Escucha")
        log_joystick_message(control_panel, "🛑 Escucha del joystick detenida")
    else:
        # Iniciar escucha
        if control_panel.joystick_controller.start_listening():
            control_panel.btn_listen.config(text="🛑 Parar Escucha")
            log_joystick_message(control_panel, "🎧 Escucha del joystick iniciada")
        else:
            log_joystick_message(control_panel, "❌ Error al iniciar escucha")

def test_buttons_action(control_panel):
    """Acción para probar botones del joystick"""
    def test_in_thread():
        log_joystick_message(control_panel, "🧪 Modo prueba iniciado - Presiona botones para verlos")
        # Aquí podrías implementar un modo de prueba temporal
        # Por ahora solo mostramos el mensaje
    
    threading.Thread(target=test_in_thread, daemon=True).start()
